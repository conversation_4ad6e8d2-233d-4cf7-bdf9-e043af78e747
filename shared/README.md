# 共享资源目录 (Shared Resources)

这个目录包含所有项目共享的资源、工具函数、配置文件和文档模板，旨在提高代码复用性，减少重复开发，确保项目间的一致性。

## 🎯 目录设计理念

### 核心原则
- **代码复用**: 避免在不同项目中重复编写相同功能
- **统一标准**: 确保所有项目遵循一致的编码规范和配置标准
- **模块化设计**: 将通用功能抽象为独立模块，便于维护和更新
- **文档驱动**: 为所有共享资源提供详细的使用文档

## 📁 目录结构详解

### 📂 `utils/` - 通用工具函数库
**用途**: 存放可在多个项目中复用的工具函数和脚本
**内容规划**:
```
utils/
├── trading/              # 交易相关工具函数
│   ├── risk-calculator.js    # 风险计算工具
│   ├── position-sizer.js     # 仓位计算工具
│   └── market-analyzer.js    # 市场分析工具
├── data/                 # 数据处理工具
│   ├── validator.js          # 数据验证工具
│   ├── formatter.js          # 数据格式化工具
│   └── converter.js          # 数据转换工具
├── ui/                   # 用户界面工具
│   ├── chart-helpers.js      # 图表辅助函数
│   ├── form-validator.js     # 表单验证工具
│   └── responsive-utils.js   # 响应式设计工具
└── common/               # 通用工具函数
    ├── math-utils.js         # 数学计算工具
    ├── date-utils.js         # 日期处理工具
    └── string-utils.js       # 字符串处理工具
```

### 📂 `configs/` - 共享配置文件
**用途**: 存放项目间共享的配置文件和常量定义
**内容规划**:
```
configs/
├── trading/              # 交易相关配置
│   ├── symbols.json          # 交易品种配置
│   ├── timeframes.json       # 时间周期配置
│   └── risk-levels.json      # 风险等级配置
├── ui/                   # 界面配置
│   ├── themes.json           # 主题配置
│   ├── colors.json           # 颜色配置
│   └── layouts.json          # 布局配置
├── api/                  # API配置
│   ├── endpoints.json        # API端点配置
│   └── headers.json          # 请求头配置
└── common/               # 通用配置
    ├── constants.json        # 常量定义
    ├── defaults.json         # 默认值配置
    └── validation-rules.json # 验证规则配置
```

### 📂 `docs/` - 共享文档和模板
**用途**: 存放项目文档模板、编码规范和最佳实践指南
**内容规划**:
```
docs/
├── templates/            # 文档模板
│   ├── README-template.md    # README模板
│   ├── API-doc-template.md   # API文档模板
│   └── CHANGELOG-template.md # 更新日志模板
├── guidelines/           # 开发指南
│   ├── coding-standards.md   # 编码规范
│   ├── git-workflow.md       # Git工作流程
│   └── testing-guide.md      # 测试指南
├── architecture/         # 架构文档
│   ├── system-design.md      # 系统设计文档
│   ├── data-flow.md          # 数据流设计
│   └── security-guide.md     # 安全指南
└── examples/             # 示例代码
    ├── component-examples/   # 组件示例
    ├── function-examples/    # 函数示例
    └── integration-examples/ # 集成示例
```

## 🚀 使用指南

### 添加新的共享资源

#### 1. 添加工具函数
```javascript
// 示例：添加新的交易工具函数
// 文件：shared/utils/trading/profit-calculator.js

/**
 * 计算交易盈亏
 * @param {number} entryPrice - 入场价格
 * @param {number} exitPrice - 出场价格
 * @param {number} lotSize - 手数
 * @param {string} direction - 交易方向 ('long' | 'short')
 * @returns {number} 盈亏金额
 */
export function calculateProfit(entryPrice, exitPrice, lotSize, direction) {
    const priceDiff = direction === 'long'
        ? exitPrice - entryPrice
        : entryPrice - exitPrice;
    return priceDiff * lotSize * 100; // XAUUSD点值
}
```

#### 2. 添加配置文件
```json
// 示例：添加新的配置文件
// 文件：shared/configs/trading/brokers.json
{
    "brokers": {
        "default": {
            "spread": 0.3,
            "commission": 0,
            "leverage": 100
        },
        "premium": {
            "spread": 0.1,
            "commission": 5,
            "leverage": 500
        }
    }
}
```

#### 3. 添加文档模板
```markdown
<!-- 示例：添加新的文档模板 -->
<!-- 文件：shared/docs/templates/FEATURE-template.md -->
# 功能名称

## 概述
简要描述功能的用途和价值

## 使用方法
详细的使用说明和示例

## API参考
函数签名和参数说明

## 注意事项
使用时的注意事项和限制
```

### 在项目中使用共享资源

#### 1. 引用工具函数
```javascript
// 在项目中引用共享工具函数
import { calculateProfit } from '../shared/utils/trading/profit-calculator.js';
import { validatePrice } from '../shared/utils/data/validator.js';

// 使用示例
const profit = calculateProfit(2050, 2060, 0.1, 'long');
const isValid = validatePrice(2050.50);
```

#### 2. 使用配置文件
```javascript
// 加载共享配置
import brokersConfig from '../shared/configs/trading/brokers.json';
import themeConfig from '../shared/configs/ui/themes.json';

// 使用配置
const defaultBroker = brokersConfig.brokers.default;
const primaryColor = themeConfig.colors.primary;
```

## 📋 开发最佳实践

### 代码质量标准
- **函数设计**: 单一职责，输入输出明确
- **错误处理**: 完善的异常处理和错误提示
- **性能优化**: 避免不必要的计算和内存占用
- **兼容性**: 确保在不同环境下的兼容性

### 文档要求
- **函数文档**: 使用JSDoc格式编写详细的函数文档
- **配置说明**: 为每个配置项提供清晰的说明
- **使用示例**: 提供实际的使用示例和代码片段
- **更新记录**: 记录每次修改的内容和原因

### 版本管理
- **语义化版本**: 遵循语义化版本规范
- **变更日志**: 详细记录每个版本的变更内容
- **向后兼容**: 尽量保持向后兼容性
- **废弃通知**: 提前通知即将废弃的功能

### 测试要求
- **单元测试**: 为所有工具函数编写单元测试
- **集成测试**: 测试与其他模块的集成情况
- **性能测试**: 确保性能满足要求
- **兼容性测试**: 在不同环境下进行测试

## 🔧 维护指南

### 定期维护任务
1. **代码审查**: 定期审查共享代码的质量
2. **依赖更新**: 及时更新依赖库和工具
3. **性能监控**: 监控共享资源的性能表现
4. **文档更新**: 保持文档与代码的同步

### 重构原则
- **渐进式重构**: 避免大规模的破坏性重构
- **测试保护**: 重构前确保有充分的测试覆盖
- **通知机制**: 重构前通知所有使用者
- **迁移指南**: 提供详细的迁移指南

## 📊 当前状态

### 目录清理完成
- **子目录**: 已清理（configs/, docs/, utils/）
- **保留文件**: 仅保留README.md
- **状态**: 目录已简化，等待按需添加资源

### 使用建议
- 根据实际需要添加共享资源
- 遵循上述目录结构规划
- 保持代码质量和文档完整性

---

**维护状态**: ✅ 已清理
**最后更新**: 2025-08-13
**说明**: 目录已清理，仅保留主要文件
