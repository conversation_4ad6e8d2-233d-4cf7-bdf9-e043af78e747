# 交易工具集合 - Monorepo

这是一个专注于金融交易工具开发的单一仓库（Monorepo），包含了完整的XAUUSD（黄金）交易工具链和MT4自动化交易系统。所有项目都经过精心设计，旨在为交易者提供专业、可靠的交易辅助工具。

## 🎯 项目愿景

构建一套完整的交易工具生态系统，涵盖风险管理、仓位计算、交易记录、策略分析等交易活动的各个环节，帮助交易者提升交易效率和风险控制能力。

## 📁 项目架构

```
trading-tools-monorepo/
├── 🏆 GoldProfessionalStrategy/   # 黄金专业版策略 (MT5)
│   ├── GoldProfessional.mq5      # 专业黄金交易EA
│   └── README.md                 # 详细策略说明
├── 🔄 GoldTradeSetup_MQL5_Conversion/ # 黄金交易设置EA (MT5)
│   ├── GoldTradeSetupEA.mq5      # AMA+SuperTrend+月相策略
│   └── README.md                 # 策略文档
├── 📈 MT5_TrendBreakout_Strategy/ # MT5趋势突破增强策略
│   ├── TrendBreakout_Enhanced_EA.mq5 # 增强版EA (主文件)
│   └── README.md                 # 完整策略文档
├── 🎯 TrendBreakoutReversalEA/    # 趋势突破与反转策略 (MT4)
│   ├── TrendBreakoutReversal2.mq4 # 策略EA (主文件)
│   └── README.md                 # 策略说明
├── 📊 XAUUSD_LiquidityGrab_ScalpingEA/ # XAUUSD流动性抓取剥头皮策略 (MT5)
│   ├── XAUUSD_LiquidityGrab_ScalpingEA.mq5 # 流动性抓取策略EA
│   └── README.md                 # 策略文档
├── 📈 XAUUSD_Strategy_Analysis/   # XAUUSD策略分析 (MT5)
│   ├── XAUUSD_Strategy_MT5.mq5   # 多指标综合策略
│   └── README.md                 # 策略文档
├── 🛡️ mt4-risk-manager/          # MT4风险管理插件
│   ├── RiskManager.mq4           # 风险管理EA源码
│   └── README.md                 # 详细使用说明
├── 🤖 mt4-rsi-strategy/          # MT4 RSI累积策略EA
│   ├── rsi.ex4                   # 编译后的EA文件
│   └── README.md                 # 策略说明文档
├── 💰 xauusd-position-calculator/ # XAUUSD仓位计算器
│   ├── index.html                # 主页面 (单文件应用)
│   └── README.md                 # 使用指南
├── 📈 xauusd-trading-journal/    # XAUUSD交易日志系统
│   ├── index.html                # 主应用页面
│   └── README.md                 # 完整文档
├── 🔧 shared/                    # 共享资源库
│   └── README.md                 # 共享资源指南 (已清理)
└── 📋 README.md                  # 本文件 - 项目总览
```

## 🚀 核心项目介绍

### 1. 🏆 黄金专业版策略 (`GoldProfessionalStrategy/`)
**专业的MT5黄金交易策略**
- **类型**: MetaTrader 5 专家顾问 (EA)
- **核心功能**: 55MA通道突破、Heiken Ashi确认、智能追踪止损
- **技术特色**: HTF过滤器、动态仓位计算、完整风险管理
- **适用对象**: 专业黄金交易者，适合高波动性市场
- **开发状态**: ✅ 完成并持续优化
- **开发语言**: MQL5

### 2. 🔄 黄金交易设置EA (`GoldTradeSetup_MQL5_Conversion/`)
**结合天体周期的综合交易策略**
- **类型**: MetaTrader 5 自动交易专家顾问
- **核心策略**: AMA自适应移动平均线 + SuperTrend + 月相周期分析
- **技术优势**: 多维度分析、自适应性强、独特的天体周期过滤
- **适用市场**: XAUUSD等贵金属，适合中长期趋势交易
- **开发状态**: ✅ 完成并经过验证
- **开发语言**: MQL5

### 3. 📈 MT5趋势突破增强策略 (`MT5_TrendBreakout_Strategy/`)
**功能完整的趋势突破交易系统**
- **类型**: MetaTrader 5 专家顾问 (增强版)
- **核心功能**: 双向突破策略、K线颜色过滤、独立策略开关
- **技术特色**: 实时监控面板、多重过滤系统、智能风险管理
- **适用对象**: 适合XAUUSD 3分钟图表，支持灵活的策略配置
- **开发状态**: ✅ v2.0增强版，功能完善
- **开发语言**: MQL5

### 4. 🎯 趋势突破与反转策略 (`TrendBreakoutReversalEA/`)
**MT4双模式交易策略**
- **类型**: MetaTrader 4 专家顾问 (版本2)
- **核心策略**: 趋势突破交易 + 反转确认交易，自合成3分钟时间框架
- **技术优势**: 多重信号确认、专业监控面板、严格风险控制
- **适用市场**: 专为XAUUSD优化，适应不同市场环境
- **开发状态**: ✅ 完成并经过回测验证
- **开发语言**: MQL4

### 5. 📊 XAUUSD流动性抓取剥头皮策略 (`XAUUSD_LiquidityGrab_ScalpingEA/`)
**基于流动性抓取的高频交易策略**
- **类型**: MetaTrader 5 专家顾问
- **核心策略**: 流动性抓取机制、Wick分析、反转速度检测
- **技术特色**: 多重过滤系统、智能风险管理、高级订单管理
- **适用对象**: 专为XAUUSD剥头皮交易设计，需要低延迟环境
- **开发状态**: ✅ 完成并优化
- **开发语言**: MQL5

### 6. 📈 XAUUSD策略分析 (`XAUUSD_Strategy_Analysis/`)
**多指标综合分析策略**
- **类型**: MetaTrader 5 专家顾问
- **核心功能**: MACD + RSI + 布林带 + ATR多指标组合
- **技术特色**: 基于ATR的动态风险管理、M10时间框架优化
- **适用对象**: 适合需要多重确认的保守型交易者
- **开发状态**: ✅ 完成
- **开发语言**: MQL5

### 7. 🛡️ MT4风险管理插件 (`mt4-risk-manager/`)
**专业的MT4风险控制工具**
- **类型**: MetaTrader 4 专家顾问 (EA)
- **核心功能**: 可视化风险管理、自动仓位计算、拖拽式止损止盈设置
- **技术特色**: 实时计算、事件驱动、智能验证
- **适用对象**: 所有MT4用户，特别是注重风险控制的交易者
- **开发状态**: ✅ 完成并持续优化
- **开发语言**: MQL4

### 8. 🤖 MT4 RSI累积策略EA (`mt4-rsi-strategy/`)
**基于RSI累积算法的智能交易系统**
- **类型**: MetaTrader 4 自动交易专家顾问
- **核心策略**: RSI累积值触发交易，可选移动平均线过滤
- **技术优势**: 降噪效果好、参数可调、风险可控
- **适用市场**: 适合震荡和趋势市场，特别是XAUUSD等贵金属
- **开发状态**: ✅ 完成并经过回测验证
- **开发语言**: MQL4

### 9. 💰 XAUUSD仓位计算器 (`xauusd-position-calculator/`)
**专业的黄金交易仓位计算工具**
- **类型**: 纯前端Web应用 (单文件)
- **核心功能**: 精确仓位计算、风险评估、交易方向验证
- **技术特色**: 响应式设计、实时验证、离线运行
- **适用对象**: 所有XAUUSD交易者，特别是手动交易者
- **开发状态**: ✅ 完成并持续改进
- **技术栈**: HTML5 + CSS3 + ES6+ JavaScript

### 10. 📈 XAUUSD交易日志系统 (`xauusd-trading-journal/`)
**功能完整的交易记录与分析平台**
- **类型**: 本地化Web应用 (已简化)
- **核心功能**: 交易记录、数据分析、可视化图表、时间热力图
- **技术亮点**: Chart.js图表、localStorage存储、模块化架构
- **分析功能**: 胜率统计、盈亏分析、时间分布、策略标签分析
- **开发状态**: ✅ v2.0版本，功能完善
- **技术栈**: 原生JavaScript + Chart.js + CSS3

### 11. 🔧 共享资源库 (`shared/`)
**项目间共享的工具和资源**
- **类型**: 工具函数库和配置管理 (已清理)
- **当前状态**: 目录已简化，仅保留README文档
- **设计理念**: 模块化、可复用、文档驱动
- **开发状态**: ✅ 已清理，等待按需添加资源

## 🎨 技术特色

### 多技术栈融合
- **MQL4/MQL5**: 专业的MT4/MT5自动化交易开发，涵盖从基础到高级的各种策略
- **现代Web技术**: HTML5、CSS3、ES6+、Chart.js，构建专业的交易工具
- **响应式设计**: 完美适配桌面和移动设备，随时随地管理交易
- **模块化架构**: 清晰的代码结构，易于维护和扩展

### 策略多样性
- **趋势跟踪**: 55MA通道、EMA组合、SuperTrend等多种趋势识别方法
- **反转交易**: RSI超买超卖、布林带反弹、锤子线等反转信号
- **突破策略**: 价格突破、成交量确认、多重过滤的突破系统
- **综合分析**: 多指标组合、时间周期过滤、天体周期等创新方法

### 用户体验优先
- **直观界面**: 简洁明了的用户界面设计，专业的监控面板
- **实时反馈**: 即时的计算结果和状态更新，实时交易信号显示
- **智能验证**: 全面的输入验证和错误提示，交易方向逻辑检查
- **离线运行**: 大部分工具支持完全离线使用，保护数据隐私

### 专业级功能
- **精确计算**: 基于真实交易规则的精确算法，符合XAUUSD交易标准
- **风险控制**: 完善的风险管理和预警机制，动态仓位计算
- **数据分析**: 深度的交易数据分析和可视化，时间热力图分析
- **策略优化**: 支持策略回测和参数优化，多重过滤系统

## 🚀 快速开始

### 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd trading-tools-monorepo

# 对于Web项目，启动本地服务器
cd xauusd-trading-journal
python3 -m http.server 8080
# 或者
npm start
```

### 项目使用指南

#### MT4/MT5工具使用
1. **MT5黄金专业策略**:
   - 复制`GoldProfessionalStrategy/GoldProfessional.mq5`到MT5的`MQL5/Experts`目录
   - 重启MT5，将EA拖拽到XAUUSD图表上
   - 配置风险参数和时间框架，开始使用

2. **MT5趋势突破策略**:
   - 复制`MT5_TrendBreakout_Strategy/TrendBreakout_Enhanced_EA.mq5`到MT5的`MQL5/Experts`目录
   - 在XAUUSD 3分钟图表上使用
   - 根据市场环境配置策略开关

3. **MT4趋势突破反转策略**:
   - 复制`TrendBreakoutReversalEA/TrendBreakoutReversal.mq4`到MT4的`MQL4/Experts`目录
   - 在XAUUSD M1图表上使用（EA自合成3分钟时间框架）
   - 在策略测试器中回测验证

4. **MT4风险管理插件**:
   - 复制`mt4-risk-manager/RiskManager.mq4`到MT4的`MQL4/Experts`目录
   - 重启MT4，将EA拖拽到图表上
   - 配置风险参数，开始使用

5. **MT4 RSI策略EA**:
   - 复制`mt4-rsi-strategy/rsi.ex4`到MT4的`MQL4/Experts`目录
   - 在策略测试器中回测验证
   - 配置参数后部署到实盘或模拟账户

#### Web工具使用
1. **仓位计算器**:
   - 直接在浏览器中打开`xauusd-position-calculator/index.html`
   - 输入交易参数，获取仓位建议

2. **交易日志系统**:
   - 启动本地服务器，访问`http://localhost:8080`
   - 开始记录和分析您的交易数据

## 📊 项目统计

### 开发进度
| 项目 | 状态 | 版本 | 最后更新 |
|------|------|------|----------|
| 黄金专业版策略 (MT5) | ✅ 完成 | v1.0 | 2025-08-13 |
| 黄金交易设置EA (MT5) | ✅ 完成 | v1.0 | 2025-08-13 |
| MT5趋势突破增强策略 | ✅ 完成 | v2.0 | 2025-08-13 |
| 趋势突破与反转策略 (MT4) | ✅ 完成 | v1.0 | 2025-08-13 |
| XAUUSD流动性抓取剥头皮策略 (MT5) | ✅ 完成 | v1.0 | 2025-08-13 |
| XAUUSD策略分析 (MT5) | ✅ 完成 | v1.0 | 2025-08-13 |
| MT4风险管理插件 | ✅ 完成 | v1.0 | 2025-08-13 |
| MT4 RSI策略EA | ✅ 完成 | v1.0 | 2025-08-13 |
| XAUUSD仓位计算器 | ✅ 完成 | v1.0 | 2025-08-13 |
| XAUUSD交易日志系统 | ✅ 完成 | v2.0 | 2025-08-13 |
| 共享资源库 | ✅ 已清理 | v1.0 | 2025-08-13 |

### 技术指标
- **总代码行数**: 约12000+行 (清理后)
- **支持的交易品种**: 主要针对XAUUSD，可扩展到其他品种
- **MT4/MT5策略数量**: 8个专业交易策略
- **Web应用数量**: 2个专业交易工具 (已简化)
- **浏览器兼容性**: Chrome 60+, Firefox 55+, Safari 12+, Edge 79+
- **MT4兼容性**: Build 1090+
- **MT5兼容性**: Build 3000+

## 📋 项目管理

### 开发工作流
1. **需求分析**: 明确项目目标和用户需求
2. **技术选型**: 选择合适的技术栈和工具
3. **原型设计**: 创建功能原型和界面设计
4. **开发实现**: 编码实现核心功能
5. **测试验证**: 全面测试功能和性能
6. **文档编写**: 编写详细的使用文档
7. **发布部署**: 发布稳定版本

### 版本管理策略
- **语义化版本**: 遵循`主版本.次版本.修订版本`格式
- **独立版本**: 每个项目独立管理版本号
- **标签管理**: 使用Git标签标记重要版本：`project-name/v1.0.0`
- **变更日志**: 详细记录每个版本的变更内容

### 代码质量标准
- **编码规范**: 统一的代码风格和命名规范
- **文档要求**: 详细的代码注释和使用文档
- **测试覆盖**: 关键功能的测试验证
- **性能优化**: 持续的性能监控和优化

## 🛠️ 开发环境

### 必需工具
- **Git**: 版本控制系统
- **现代浏览器**: Chrome/Firefox/Safari/Edge最新版
- **代码编辑器**: VS Code（推荐）或其他现代编辑器

### MT4开发环境
- **MetaTrader 4**: 官方交易平台
- **MQL4编辑器**: 内置的代码编辑器
- **策略测试器**: 用于EA回测和优化

### Web开发环境
- **Python 3.x**: 用于本地HTTP服务器
- **Node.js**: 可选，用于包管理和构建工具
- **现代浏览器开发者工具**: 用于调试和性能分析

## 🤝 贡献指南

### 贡献流程
1. **Fork项目**: 创建项目的分支
2. **创建功能分支**: `git checkout -b feature/new-feature`
3. **开发功能**: 实现新功能或修复bug
4. **编写测试**: 为新功能编写相应的测试
5. **更新文档**: 更新相关的文档和README
6. **提交代码**: 使用清晰的提交信息
7. **创建Pull Request**: 提交代码审查请求

### 代码贡献规范
- **代码风格**: 遵循项目的编码规范
- **提交信息**: 使用清晰、描述性的提交信息
- **文档更新**: 确保文档与代码保持同步
- **测试要求**: 新功能需要包含相应的测试

### 问题反馈
- **Bug报告**: 详细描述问题现象和复现步骤
- **功能建议**: 清晰描述建议的功能和使用场景
- **文档改进**: 指出文档中的错误或不清晰的地方

## 📄 许可证信息

### 开源许可
本项目采用MIT许可证，允许自由使用、修改和分发。详细信息请查看各项目目录下的LICENSE文件。

### 免责声明
**重要提示**: 本项目中的所有工具仅用于教育和研究目的，不构成任何形式的投资建议。金融交易存在高风险，可能导致资金损失。使用者应：
- 充分了解相关风险
- 在模拟环境中充分测试
- 根据自身情况谨慎决策
- 承担使用工具的所有风险

## 📞 支持与联系

### 获取帮助
- **文档查阅**: 详细阅读各项目的README文件
- **问题排查**: 检查常见问题和解决方案
- **社区支持**: 参与相关的交易技术社区讨论

### 项目维护
- **维护状态**: 🟢 积极维护
- **更新频率**: 根据需求和反馈定期更新
- **长期支持**: 承诺长期维护和支持

---

**项目信息**:
📅 **创建时间**: 2024年
📅 **最后更新**: 2025-08-13
🏷️ **当前版本**: 各项目独立版本管理
👨‍💻 **维护状态**: 积极开发和维护中
🌟 **项目愿景**: 构建完整的交易工具生态系统
🧹 **清理状态**: ✅ 已完成目录清理，仅保留核心文件
